# Mandy - Emotionally Intelligent AI Companion

Mandy is a production-grade, microservice-driven, emotionally intelligent AI companion designed for authentic, long-term human connection. Built with a memory-first architecture using temporal knowledge graphs.

## 🏗️ Architecture Overview

Mandy is composed of independently deployable microservices:

```
┌───────────────────────────────┐
│       Gradio Web UI           │
│ (User ID + Password Auth)     │
└──────────────┬────────────────┘
               | (Secure API Call)
               ↓
┌───────────────────────────────────────┐
│      Core Chat Engine (Gemini)        │
│   - Orchestrates all logic            │
│   - Manages Typing & Tone             │
└──────────────┬────────────────┘
               | (Graphiti REST API Call)
               ↓
┌───────────────────────────────────────────────┐
│          Graphiti Server (REST/MCP)           │
│    (The single entrypoint for all memory)     │
└───────────────────────┬───────────────────────┘
                        | (Native Driver Connection)
                        ↓
      ┌───────────────────────────────┐
      │     Neo4j Database Instance   │
      │ (<PERSON>'s Physical Brain)      │
      └───────────────────────────────┘
```

## 🧠 Core Technology Stack

- **Intelligence Engine**: Google Gemini 1.5 Pro/Flash
- **Memory System**: Graphiti Framework with Neo4j
- **API Framework**: Python FastAPI
- **UI Framework**: Gradio
- **Background Processing**: APScheduler
- **Containerization**: Docker & Docker Compose

## 🚀 Quick Start

### Prerequisites

- Python 3.10+
- Docker & Docker Compose
- Google Gemini API Key
- Neo4j Database (or use Docker setup)

### Environment Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd mandy
```

2. Copy environment configuration:
```bash
cp .env.example .env
```

3. Edit `.env` with your API keys and configuration:
   - Set `GEMINI_API_KEY` to your Google Gemini API key
   - Set `SECRET_KEY` to a secure random string
   - Configure Neo4j credentials if using external database

4. **Quick Development Start:**
```bash
python start_dev.py
```
This script will:
- Check your environment
- Install dependencies
- Start Neo4j in Docker
- Launch the Core Chat Engine
- Provide testing instructions

5. **Manual Docker Setup:**
```bash
docker-compose up -d
```

6. **Test the API:**
```bash
python test_api.py
```

### Access Points
- Core API: http://localhost:8000
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health
- Neo4j Browser: http://localhost:7474 (neo4j/password)

## 📁 Project Structure

```
mandy/
├── core_chat_engine/          # Main conversation orchestrator
│   ├── app/
│   ├── models/
│   ├── services/
│   └── Dockerfile
├── workers/                   # Background processing services
│   ├── content_fetcher/
│   ├── reflection_engine/
│   └── Dockerfile
├── ui/                        # Gradio web interface
│   ├── components/
│   ├── auth/
│   └── Dockerfile
├── shared/                    # Shared utilities and models
│   ├── auth/
│   ├── models/
│   └── utils/
├── docker-compose.yml         # Local development setup
├── docker-compose.prod.yml    # Production deployment
├── .env.example              # Environment template
└── README.md
```

## 🔧 Configuration

### Environment Variables

Key environment variables (see `.env.example` for complete list):

- `GEMINI_API_KEY`: Google Gemini API key
- `NEO4J_URI`: Neo4j database connection
- `NEO4J_USER`: Neo4j username
- `NEO4J_PASSWORD`: Neo4j password
- `SECRET_KEY`: JWT secret for authentication
- `ENVIRONMENT`: deployment environment (dev/staging/prod)

### Service Configuration

Each service can be configured independently:
- Core Chat Engine: Port 8000
- Workers: Background processes
- UI: Port 7860
- Neo4j: Port 7687 (bolt), 7474 (browser)

## 🧪 Development

### What's Currently Implemented

✅ **Core Chat Engine** (Complete)
- FastAPI-based REST API with full authentication
- Google Gemini integration for AI responses
- Graphiti framework integration for temporal memory
- Emotion analysis and sentiment detection
- Human-like typing engine with message bursts
- Comprehensive logging and monitoring
- Health checks and error handling

✅ **Project Infrastructure** (Complete)
- Docker containerization for all services
- Environment configuration management
- Production-ready deployment configs
- Comprehensive test suite
- Development tools and scripts

🚧 **In Progress**
- Background worker services
- Gradio web interface
- Advanced memory operations

### Local Development

1. **Quick Start (Recommended):**
```bash
python start_dev.py
```

2. **Manual Setup:**
```bash
# Install dependencies
pip install -r requirements.txt

# Start Neo4j
docker run -d --name mandy-neo4j-dev -p 7474:7474 -p 7687:7687 -e NEO4J_AUTH=neo4j/password neo4j:5.26

# Start Core Chat Engine
cd core_chat_engine && uvicorn app.main:app --reload --port 8000
```

### Testing

```bash
# Run comprehensive test suite
pytest tests/ -v

# Test API endpoints
python test_api.py

# Manual API testing
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### API Usage Examples

1. **Login and get token:**
```bash
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

2. **Send a message to Mandy:**
```bash
curl -X POST "http://localhost:8000/chat/send" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello Mandy, how are you today?"}'
```

3. **Check memory stats:**
```bash
curl -X GET "http://localhost:8000/memory/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🚀 Deployment

### Free-Tier Platforms

Mandy is designed for deployment on free-tier platforms:

- **Render**: Use `render.yaml` configuration
- **Railway**: Use `railway.json` configuration
- **Hugging Face Spaces**: Use `spaces/` configuration

### Production Deployment

1. Use production Docker Compose:
```bash
docker-compose -f docker-compose.prod.yml up -d
```

2. Configure environment for production
3. Set up monitoring and logging
4. Configure SSL/TLS certificates

## 🔒 Security

- JWT-based authentication
- API key management
- Secure inter-service communication
- User data isolation via group_id
- Environment-based configuration

## 📊 Monitoring

- Health checks for all services
- Structured logging with correlation IDs
- Performance metrics
- Error tracking and alerting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

[License information]

## 🆘 Support

- Documentation: [Link to docs]
- Issues: [GitHub Issues]
- Discord: [Community Discord]

---

Built with ❤️ for authentic AI companionship
