"""
Main application entry point for <PERSON>'s Core Chat Engine.

This module initializes the FastAPI application, sets up middleware,
configures routes, and handles startup/shutdown events.
"""

import time
from contextlib import asynccontextmanager
from typing import Dict, List

from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from shared.utils.config import get_config
from shared.utils.logging import (
    log_api_request,
    set_request_context,
    setup_logging,
)

from app.routes import auth, chat, health, history, memory
from app.services.graphiti_service import get_graphiti_client


# Configuration
config = get_config()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Manage application lifecycle events.
    
    This context manager handles startup and shutdown events for the application,
    including initializing services and cleaning up resources.
    """
    # Startup: Initialize services
    setup_logging(service_name="core_chat_engine", log_level=config.logging.log_level)
    
    # Initialize Graphiti client
    graphiti_client = get_graphiti_client()
    
    # Yield control to the application
    yield
    
    # Shutdown: Clean up resources
    # Close any open connections, etc.
    pass


# Create FastAPI application
app = FastAPI(
    title="Mandy Core Chat Engine",
    description="Core API for Mandy, an emotionally intelligent AI companion",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if config.service.debug else None,
    redoc_url="/redoc" if config.service.debug else None,
)


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.security.cors_origins,
    allow_credentials=config.security.cors_allow_credentials,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Request middleware for logging and context
@app.middleware("http")
async def request_middleware(request: Request, call_next):
    """
    Process each request for logging, timing, and context tracking.
    
    This middleware:
    1. Sets a unique request ID for tracking
    2. Times the request duration
    3. Logs request details
    4. Handles exceptions gracefully
    """
    # Generate request ID and set context
    request_id = set_request_context()
    
    # Extract user ID from auth header if available
    user_id = None
    if "Authorization" in request.headers:
        # This is a placeholder - actual extraction would happen in auth middleware
        pass
    
    # Start timer
    start_time = time.time()
    
    try:
        # Process the request
        response = await call_next(request)
        
        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000
        
        # Log successful request
        log_api_request(
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            duration_ms=duration_ms,
            request_id=request_id,
            user_id=user_id,
        )
        
        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id
        return response
        
    except Exception as e:
        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000
        
        # Log failed request
        log_api_request(
            method=request.method,
            path=request.url.path,
            status_code=500,
            duration_ms=duration_ms,
            request_id=request_id,
            user_id=user_id,
            error=str(e),
        )
        
        # Return error response
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": "Internal server error",
                "errors": [str(e)],
                "request_id": request_id,
            },
        )


# Include routers
app.include_router(health.router, tags=["Health"])
app.include_router(auth.router, prefix="/auth", tags=["Authentication"])
app.include_router(chat.router, prefix="/chat", tags=["Chat"])
app.include_router(history.router, prefix="/history", tags=["History"])
app.include_router(memory.router, prefix="/memory", tags=["Memory"])


# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    """Root endpoint that returns API information."""
    return {
        "name": "Mandy Core Chat Engine",
        "version": "1.0.0",
        "status": "online",
    }


# Handle 404 errors
@app.exception_handler(404)
async def not_found_exception_handler(request: Request, exc):
    """Handle 404 errors with a custom response."""
    return JSONResponse(
        status_code=404,
        content={
            "success": False,
            "message": "The requested resource was not found",
            "path": request.url.path,
        },
    )


# Handle validation errors
@app.exception_handler(422)
async def validation_exception_handler(request: Request, exc):
    """Handle validation errors with a custom response."""
    errors = []
    for error in exc.errors():
        errors.append({
            "location": error["loc"],
            "message": error["msg"],
            "type": error["type"],
        })
    
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "Validation error",
            "errors": errors,
        },
    )
