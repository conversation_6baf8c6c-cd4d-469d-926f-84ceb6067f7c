"""
Authentication routes for user login, registration, and token management.

This module handles user authentication, JWT token creation and validation,
and user session management for the Mandy AI companion.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional
from uuid import uuid4

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

from shared.auth.jwt_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, AuthenticationError
from shared.models.base import User, UserRole, APIResponse
from shared.utils.logging import get_logger, log_security_event

from app.services.user_service import UserService, get_user_service


# Initialize router and security
router = APIRouter()
security = HTTPBearer()

# Logger
logger = get_logger("auth")

# JWT handler
jwt_handler = JWTHandler()


# Request/Response models
class LoginRequest(BaseModel):
    """Login request model."""
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=6)


class RegisterRequest(BaseModel):
    """Registration request model."""
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=6)
    email: Optional[str] = Field(None, regex=r'^[^@]+@[^@]+\.[^@]+$')


class TokenResponse(BaseModel):
    """Token response model."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # seconds
    user: dict


class RefreshTokenRequest(BaseModel):
    """Refresh token request model."""
    refresh_token: str


# Dependency to get current user from token
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    user_service: UserService = Depends(get_user_service),
) -> User:
    """
    Get the current authenticated user from JWT token.
    
    Args:
        credentials: HTTP authorization credentials
        user_service: User service dependency
    
    Returns:
        User: Current authenticated user
    
    Raises:
        HTTPException: If token is invalid or user not found
    """
    try:
        # Extract token from credentials
        token = credentials.credentials
        
        # Verify token and get user info
        user_info = jwt_handler.get_user_info_from_token(token)
        user_id = user_info["user_id"]
        
        # Get user from service
        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Inactive user",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return user
        
    except ValueError as e:
        logger.warning(f"Token validation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post(
    "/login",
    response_model=APIResponse,
    summary="User login",
    description="Authenticate user and return access and refresh tokens",
)
async def login(
    request: LoginRequest,
    user_service: UserService = Depends(get_user_service),
):
    """
    Authenticate user and return JWT tokens.
    
    Args:
        request: Login request with username and password
        user_service: User service dependency
    
    Returns:
        APIResponse: Response with tokens and user information
    """
    try:
        # Authenticate user
        user = await user_service.authenticate_user(request.username, request.password)
        
        if not user:
            log_security_event(
                event_type="login_failed",
                details={"username": request.username, "reason": "invalid_credentials"},
                severity="WARNING",
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
            )
        
        # Create tokens
        access_token = jwt_handler.create_access_token(user)
        refresh_token = jwt_handler.create_refresh_token(user)
        
        # Update user's last interaction
        await user_service.update_last_interaction(user.id)
        
        # Log successful login
        log_security_event(
            event_type="login_success",
            details={"user_id": str(user.id), "username": user.username},
            severity="INFO",
        )
        
        # Prepare response data
        token_data = TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=jwt_handler.access_token_expire_minutes * 60,
            user={
                "id": str(user.id),
                "username": user.username,
                "role": user.role.value,
                "group_id": user.group_id,
                "trust_score": user.trust_score,
            },
        )
        
        return APIResponse(
            success=True,
            message="Login successful",
            data=token_data.dict(),
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed",
        )


@router.post(
    "/register",
    response_model=APIResponse,
    summary="User registration",
    description="Register a new user account",
)
async def register(
    request: RegisterRequest,
    user_service: UserService = Depends(get_user_service),
):
    """
    Register a new user account.
    
    Args:
        request: Registration request with user details
        user_service: User service dependency
    
    Returns:
        APIResponse: Response with registration status
    """
    try:
        # Check if username already exists
        existing_user = await user_service.get_user_by_username(request.username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered",
            )
        
        # Create new user
        user = User(
            username=request.username,
            email=request.email,
            role=UserRole.USER,
            group_id=f"user_{uuid4().hex[:8]}",  # Unique group ID for memory isolation
        )
        
        # Register user
        created_user = await user_service.create_user(user, request.password)
        
        # Log successful registration
        log_security_event(
            event_type="user_registered",
            details={"user_id": str(created_user.id), "username": created_user.username},
            severity="INFO",
        )
        
        return APIResponse(
            success=True,
            message="User registered successfully",
            data={
                "user_id": str(created_user.id),
                "username": created_user.username,
                "group_id": created_user.group_id,
            },
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed",
        )


@router.post(
    "/refresh",
    response_model=APIResponse,
    summary="Refresh access token",
    description="Get a new access token using a refresh token",
)
async def refresh_token(
    request: RefreshTokenRequest,
    user_service: UserService = Depends(get_user_service),
):
    """
    Refresh access token using a valid refresh token.
    
    Args:
        request: Refresh token request
        user_service: User service dependency
    
    Returns:
        APIResponse: Response with new access token
    """
    try:
        # Verify refresh token
        token_data = jwt_handler.verify_token(request.refresh_token)
        
        if token_data.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token type",
            )
        
        # Get user
        user_id = token_data.get("sub")
        user = await user_service.get_user_by_id(user_id)
        
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive",
            )
        
        # Create new access token
        access_token = jwt_handler.create_access_token(user)
        
        return APIResponse(
            success=True,
            message="Token refreshed successfully",
            data={
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": jwt_handler.access_token_expire_minutes * 60,
            },
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed",
        )


@router.post(
    "/logout",
    response_model=APIResponse,
    summary="User logout",
    description="Logout user and invalidate tokens",
)
async def logout(current_user: User = Depends(get_current_user)):
    """
    Logout user and invalidate tokens.
    
    Args:
        current_user: Current authenticated user
    
    Returns:
        APIResponse: Logout confirmation
    """
    # Log logout event
    log_security_event(
        event_type="logout",
        details={"user_id": str(current_user.id), "username": current_user.username},
        severity="INFO",
    )
    
    # In a production system, you would invalidate the tokens here
    # This could involve adding them to a blacklist or using a token store
    
    return APIResponse(
        success=True,
        message="Logout successful",
    )


@router.get(
    "/me",
    response_model=APIResponse,
    summary="Get current user",
    description="Get information about the currently authenticated user",
)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """
    Get information about the currently authenticated user.
    
    Args:
        current_user: Current authenticated user
    
    Returns:
        APIResponse: User information
    """
    return APIResponse(
        success=True,
        message="User information retrieved",
        data={
            "id": str(current_user.id),
            "username": current_user.username,
            "email": current_user.email,
            "role": current_user.role.value,
            "group_id": current_user.group_id,
            "trust_score": current_user.trust_score,
            "interaction_count": current_user.interaction_count,
            "last_interaction": current_user.last_interaction,
            "created_at": current_user.created_at,
            "preferences": current_user.preferences,
        },
    )
