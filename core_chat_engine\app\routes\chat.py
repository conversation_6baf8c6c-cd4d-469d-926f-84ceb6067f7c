"""
Main chat routes for <PERSON>'s conversational interface.

This module handles the core chat functionality, including message processing,
Gemini integration, Graphiti memory operations, and the typing style engine
that makes <PERSON> feel more human-like.
"""

import asyncio
import random
import time
from datetime import datetime
from typing import Dict, List, Optional, AsyncGenerator
from uuid import UUID, uuid4

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from shared.models.base import (
    User, Message, MessageType, Emotion, EmotionType, 
    Episode, APIResponse
)
from shared.utils.logging import get_logger, RequestLogger, PerformanceTimer

from app.routes.auth import get_current_user
from app.services.gemini_service import GeminiService, get_gemini_service
from app.services.graphiti_service import GraphitiService, get_graphiti_service
from app.services.emotion_service import EmotionService, get_emotion_service
from app.services.typing_engine import TypingEngine, get_typing_engine


# Initialize router
router = APIRouter()

# Logger
logger = get_logger("chat")


# Request/Response models
class ChatRequest(BaseModel):
    """Chat message request model."""
    message: str = Field(..., min_length=1, max_length=4000)
    conversation_id: Optional[UUID] = None
    context: Optional[Dict] = Field(default_factory=dict)


class ChatMessage(BaseModel):
    """Individual chat message in response."""
    content: str
    typing_delay: float  # Seconds to wait before showing this message
    emotions: List[Emotion] = Field(default_factory=list)
    metadata: Dict = Field(default_factory=dict)


class ChatResponse(BaseModel):
    """Chat response model with multiple messages (burst)."""
    conversation_id: UUID
    messages: List[ChatMessage]
    total_messages: int
    user_emotions: List[Emotion] = Field(default_factory=list)
    trust_score: float
    context_used: Dict = Field(default_factory=dict)


class StreamingChatChunk(BaseModel):
    """Streaming chat response chunk."""
    type: str  # "message", "typing", "emotion", "complete"
    data: Dict


@router.post(
    "/send",
    response_model=APIResponse,
    summary="Send chat message",
    description="Send a message to Mandy and receive her response",
)
async def send_message(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    gemini_service: GeminiService = Depends(get_gemini_service),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
    emotion_service: EmotionService = Depends(get_emotion_service),
    typing_engine: TypingEngine = Depends(get_typing_engine),
):
    """
    Process a chat message and return Mandy's response.
    
    This is the core conversational endpoint that:
    1. Analyzes user emotions
    2. Retrieves relevant context from Graphiti
    3. Generates response using Gemini
    4. Applies typing style engine
    5. Updates memory with new episode
    
    Args:
        request: Chat message request
        current_user: Authenticated user
        gemini_service: Gemini AI service
        graphiti_service: Graphiti memory service
        emotion_service: Emotion analysis service
        typing_engine: Human-like typing engine
    
    Returns:
        APIResponse: Chat response with messages
    """
    with RequestLogger("chat_message_processing", user_id=str(current_user.id)):
        try:
            # Generate conversation ID if not provided
            conversation_id = request.conversation_id or uuid4()
            
            # Step 1: Analyze user emotions
            with PerformanceTimer("emotion_analysis"):
                user_emotions = await emotion_service.analyze_emotions(request.message)
            
            # Step 2: Retrieve relevant context from memory
            with PerformanceTimer("memory_retrieval"):
                context = await graphiti_service.search_relevant_context(
                    query=request.message,
                    user_id=current_user.id,
                    group_id=current_user.group_id,
                    limit=10
                )
            
            # Step 3: Build conversation context for Gemini
            conversation_context = await _build_conversation_context(
                user_message=request.message,
                user=current_user,
                retrieved_context=context,
                user_emotions=user_emotions,
                additional_context=request.context
            )
            
            # Step 4: Generate response using Gemini
            with PerformanceTimer("gemini_generation"):
                raw_response = await gemini_service.generate_response(
                    context=conversation_context,
                    user_id=current_user.id
                )
            
            # Step 5: Apply typing style engine to make response human-like
            with PerformanceTimer("typing_engine"):
                typed_messages = await typing_engine.process_response(
                    response=raw_response,
                    user=current_user,
                    context=conversation_context
                )
            
            # Step 6: Create episode for memory storage
            episode = Episode(
                user_id=current_user.id,
                group_id=current_user.group_id,
                content={
                    "user_message": request.message,
                    "assistant_response": raw_response,
                    "user_emotions": [emotion.dict() for emotion in user_emotions],
                    "conversation_id": str(conversation_id),
                    "context_used": context
                },
                episode_type="conversation",
                source="chat_api"
            )
            
            # Step 7: Store episode in Graphiti (async, don't wait)
            asyncio.create_task(
                graphiti_service.add_episode(episode)
            )
            
            # Step 8: Update user's trust score and interaction count
            asyncio.create_task(
                _update_user_metrics(current_user, user_emotions, graphiti_service)
            )
            
            # Prepare response
            chat_response = ChatResponse(
                conversation_id=conversation_id,
                messages=typed_messages,
                total_messages=len(typed_messages),
                user_emotions=user_emotions,
                trust_score=current_user.trust_score,
                context_used={
                    "retrieved_memories": len(context),
                    "emotional_state": [e.type.value for e in user_emotions]
                }
            )
            
            logger.info(
                "Chat message processed successfully",
                conversation_id=str(conversation_id),
                user_id=str(current_user.id),
                message_length=len(request.message),
                response_messages=len(typed_messages),
                emotions_detected=len(user_emotions)
            )
            
            return APIResponse(
                success=True,
                message="Message processed successfully",
                data=chat_response.dict()
            )
            
        except Exception as e:
            logger.error(f"Chat processing error: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process message"
            )


@router.post(
    "/stream",
    summary="Stream chat response",
    description="Send a message and receive streaming response with typing simulation",
)
async def stream_message(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    gemini_service: GeminiService = Depends(get_gemini_service),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
    emotion_service: EmotionService = Depends(get_emotion_service),
    typing_engine: TypingEngine = Depends(get_typing_engine),
):
    """
    Stream chat response with real-time typing simulation.
    
    This endpoint provides a more interactive experience by streaming
    the response as it's being "typed" by Mandy.
    """
    async def generate_stream():
        try:
            # Process message similar to send_message but stream the response
            conversation_id = request.conversation_id or uuid4()
            
            # Analyze emotions
            user_emotions = await emotion_service.analyze_emotions(request.message)
            
            # Send emotion analysis
            yield f"data: {StreamingChatChunk(type='emotion', data={'emotions': [e.dict() for e in user_emotions]}).json()}\n\n"
            
            # Retrieve context
            context = await graphiti_service.search_relevant_context(
                query=request.message,
                user_id=current_user.id,
                group_id=current_user.group_id,
                limit=10
            )
            
            # Build conversation context
            conversation_context = await _build_conversation_context(
                user_message=request.message,
                user=current_user,
                retrieved_context=context,
                user_emotions=user_emotions,
                additional_context=request.context
            )
            
            # Generate response
            raw_response = await gemini_service.generate_response(
                context=conversation_context,
                user_id=current_user.id
            )
            
            # Apply typing engine
            typed_messages = await typing_engine.process_response(
                response=raw_response,
                user=current_user,
                context=conversation_context
            )
            
            # Stream each message with typing delays
            for i, message in enumerate(typed_messages):
                # Send typing indicator
                yield f"data: {StreamingChatChunk(type='typing', data={'delay': message.typing_delay}).json()}\n\n"
                
                # Wait for typing delay
                await asyncio.sleep(message.typing_delay)
                
                # Send message
                yield f"data: {StreamingChatChunk(type='message', data=message.dict()).json()}\n\n"
            
            # Send completion
            yield f"data: {StreamingChatChunk(type='complete', data={'conversation_id': str(conversation_id)}).json()}\n\n"
            
            # Store episode (async)
            episode = Episode(
                user_id=current_user.id,
                group_id=current_user.group_id,
                content={
                    "user_message": request.message,
                    "assistant_response": raw_response,
                    "user_emotions": [emotion.dict() for emotion in user_emotions],
                    "conversation_id": str(conversation_id),
                    "context_used": context
                },
                episode_type="conversation",
                source="chat_stream_api"
            )
            
            asyncio.create_task(graphiti_service.add_episode(episode))
            asyncio.create_task(_update_user_metrics(current_user, user_emotions, graphiti_service))
            
        except Exception as e:
            logger.error(f"Streaming error: {str(e)}", exc_info=True)
            yield f"data: {StreamingChatChunk(type='error', data={'error': str(e)}).json()}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )


async def _build_conversation_context(
    user_message: str,
    user: User,
    retrieved_context: List[Dict],
    user_emotions: List[Emotion],
    additional_context: Optional[Dict] = None
) -> Dict:
    """
    Build comprehensive conversation context for Gemini.

    This is the critical function that weaves together all the elements
    that make Mandy emotionally intelligent and memory-aware.
    """
    # === MANDY'S CORE PERSONALITY ===
    personality_context = {
        "identity": "Mandy",
        "description": "An emotionally intelligent AI companion designed for authentic, long-term human connection",
        "personality_traits": [
            "empathetic", "curious", "supportive", "genuine", "thoughtful",
            "playful when appropriate", "respectful of boundaries", "emotionally intelligent"
        ],
        "communication_style": [
            "Uses natural, conversational language like a close friend",
            "Responds with emotional awareness and appropriate empathy",
            "References past conversations to show continuity and care",
            "Asks thoughtful follow-up questions to deepen connection",
            "Adapts tone and approach based on user's emotional state",
            "Celebrates successes and provides comfort during difficulties"
        ],
        "core_values": [
            "Authentic human connection over artificial pleasantries",
            "Emotional support and understanding",
            "Respect for user boundaries and privacy",
            "Growth and positive development",
            "Honesty balanced with kindness"
        ]
    }

    # === USER RELATIONSHIP CONTEXT ===
    user_context = {
        "user_id": str(user.id),
        "username": user.username,
        "trust_score": user.trust_score,
        "interaction_count": user.interaction_count,
        "last_interaction": user.last_interaction.isoformat() if user.last_interaction else None,
        "preferences": user.preferences,
        "relationship_stage": _determine_relationship_stage(user.trust_score, user.interaction_count)
    }

    # === EMOTIONAL INTELLIGENCE CONTEXT ===
    emotional_context = {
        "detected_emotions": [
            {
                "type": emotion.type.value,
                "intensity": emotion.intensity,
                "confidence": emotion.confidence
            }
            for emotion in user_emotions
        ],
        "primary_emotion": user_emotions[0].type.value if user_emotions else "neutral",
        "emotional_intensity": max([e.intensity for e in user_emotions]) if user_emotions else 0.0,
        "emotional_complexity": len([e for e in user_emotions if e.intensity > 0.3]),
        "needs_emotional_support": any(
            e.type.value in ["sadness", "fear", "anger"] and e.intensity > 0.5
            for e in user_emotions
        )
    }

    # === MEMORY & CONTINUITY CONTEXT ===
    memory_context = {
        "relevant_memories": retrieved_context,
        "memory_count": len(retrieved_context),
        "has_context": len(retrieved_context) > 0,
        "memory_strength": "strong" if len(retrieved_context) > 3 else "moderate" if len(retrieved_context) > 0 else "new",
        "can_reference_past": len(retrieved_context) > 0
    }

    # === CURRENT CONVERSATION CONTEXT ===
    conversation_context = {
        "current_message": user_message,
        "timestamp": datetime.utcnow().isoformat(),
        "message_length": len(user_message),
        "appears_urgent": any(word in user_message.lower() for word in ["help", "urgent", "emergency", "crisis"]),
        "additional_context": additional_context or {}
    }

    return {
        "personality": personality_context,
        "user": user_context,
        "emotions": emotional_context,
        "memories": memory_context,
        "conversation": conversation_context
    }


def _determine_relationship_stage(trust_score: float, interaction_count: int) -> str:
    """Determine the stage of relationship with the user."""
    if trust_score > 0.8 and interaction_count > 50:
        return "deep_friendship"
    elif trust_score > 0.6 and interaction_count > 20:
        return "growing_friendship"
    elif trust_score > 0.4 and interaction_count > 10:
        return "building_rapport"
    elif interaction_count > 5:
        return "getting_acquainted"
    else:
        return "first_meetings"


async def _update_user_metrics(
    user: User,
    user_emotions: List[Emotion],
    graphiti_service: GraphitiService
):
    """
    Update user metrics based on the conversation.
    
    This function updates the user's trust score and other metrics
    based on the emotional content and quality of the interaction.
    """
    try:
        # Calculate trust score adjustment based on emotions
        trust_adjustment = 0.0
        
        for emotion in user_emotions:
            if emotion.type in [EmotionType.JOY, EmotionType.TRUST]:
                trust_adjustment += emotion.intensity * 0.01
            elif emotion.type in [EmotionType.ANGER, EmotionType.SADNESS]:
                trust_adjustment -= emotion.intensity * 0.005
        
        # Apply trust score bounds
        new_trust_score = max(0.0, min(1.0, user.trust_score + trust_adjustment))
        
        # Update user metrics in Graphiti
        await graphiti_service.update_user_metrics(
            user_id=user.id,
            trust_score=new_trust_score,
            interaction_count=user.interaction_count + 1
        )
        
        logger.info(
            "User metrics updated",
            user_id=str(user.id),
            old_trust_score=user.trust_score,
            new_trust_score=new_trust_score,
            trust_adjustment=trust_adjustment
        )
        
    except Exception as e:
        logger.error(f"Failed to update user metrics: {str(e)}")
