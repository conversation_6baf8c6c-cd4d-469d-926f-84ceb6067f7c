"""
Google Gemini AI service for generating conversational responses.

This service handles all interactions with Google's Gemini API,
including context preparation, response generation, and error handling.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from uuid import UUID

import google.generativeai as genai
from google.generativeai.types import Harm<PERSON>ategory, HarmBlockThreshold

from shared.utils.config import get_config
from shared.utils.logging import get_logger, PerformanceTimer, log_external_api_call


# Configuration and logger
config = get_config()
logger = get_logger("gemini_service")


class GeminiService:
    """Service for interacting with Google Gemini API."""
    
    def __init__(self):
        """Initialize Gemini service with API configuration."""
        # Configure Gemini API
        genai.configure(api_key=config.gemini.api_key)
        
        # Initialize models
        self.model = genai.GenerativeModel(
            model_name=config.gemini.model,
            generation_config=genai.types.GenerationConfig(
                temperature=config.gemini.temperature,
                max_output_tokens=config.gemini.max_tokens,
                top_p=0.8,
                top_k=40,
            ),
            safety_settings={
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }
        )
        
        self.small_model = genai.GenerativeModel(
            model_name=config.gemini.small_model,
            generation_config=genai.types.GenerationConfig(
                temperature=config.gemini.temperature,
                max_output_tokens=2048,
                top_p=0.8,
                top_k=40,
            )
        )
        
        logger.info("Gemini service initialized", model=config.gemini.model)
    
    async def generate_response(
        self,
        context: Dict[str, Any],
        user_id: UUID,
        use_small_model: bool = False
    ) -> str:
        """
        Generate a conversational response using Gemini.
        
        Args:
            context: Comprehensive conversation context
            user_id: User ID for logging
            use_small_model: Whether to use the smaller, faster model
        
        Returns:
            str: Generated response from Gemini
        """
        with PerformanceTimer("gemini_generation", user_id=str(user_id)):
            try:
                # Build the prompt from context
                prompt = self._build_prompt(context)
                
                # Choose model based on request
                model = self.small_model if use_small_model else self.model
                
                # Generate response
                response = await asyncio.to_thread(
                    model.generate_content,
                    prompt
                )
                
                # Extract text from response
                if response.candidates and response.candidates[0].content.parts:
                    generated_text = response.candidates[0].content.parts[0].text
                else:
                    logger.warning("Empty response from Gemini", user_id=str(user_id))
                    generated_text = "I'm having trouble responding right now. Could you try again?"
                
                # Log successful API call
                log_external_api_call(
                    service="gemini",
                    endpoint=config.gemini.model,
                    status_code=200,
                    duration_ms=0,  # Will be filled by PerformanceTimer
                    user_id=str(user_id)
                )
                
                logger.info(
                    "Generated Gemini response",
                    user_id=str(user_id),
                    model=model.model_name,
                    response_length=len(generated_text),
                    prompt_length=len(prompt)
                )
                
                return generated_text.strip()
                
            except Exception as e:
                logger.error(
                    f"Gemini generation failed: {str(e)}",
                    user_id=str(user_id),
                    exc_info=True
                )
                
                # Log failed API call
                log_external_api_call(
                    service="gemini",
                    endpoint=config.gemini.model,
                    status_code=500,
                    duration_ms=0,
                    user_id=str(user_id),
                    error=str(e)
                )
                
                # Return fallback response
                return self._get_fallback_response(context)
    
    async def analyze_sentiment(self, text: str) -> Dict[str, float]:
        """
        Analyze sentiment of text using Gemini.
        
        Args:
            text: Text to analyze
        
        Returns:
            Dict with sentiment scores
        """
        try:
            prompt = f"""
            Analyze the sentiment and emotional tone of the following text.
            Return a JSON object with scores (0.0 to 1.0) for:
            - positive: How positive the text is
            - negative: How negative the text is
            - neutral: How neutral the text is
            - emotional_intensity: Overall emotional intensity
            
            Text: "{text}"
            
            Return only the JSON object, no other text.
            """
            
            response = await asyncio.to_thread(
                self.small_model.generate_content,
                prompt
            )
            
            if response.candidates and response.candidates[0].content.parts:
                result_text = response.candidates[0].content.parts[0].text.strip()
                # Parse JSON response
                sentiment_data = json.loads(result_text)
                return sentiment_data
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {str(e)}")
        
        # Return neutral sentiment as fallback
        return {
            "positive": 0.5,
            "negative": 0.0,
            "neutral": 0.5,
            "emotional_intensity": 0.3
        }
    
    def _build_prompt(self, context: Dict[str, Any]) -> str:
        """
        Build a comprehensive prompt for Gemini from the conversation context.

        This is the heart of Mandy's intelligence - dynamic prompt engineering
        that weaves together personality, memories, emotions, and context.

        Args:
            context: Conversation context dictionary

        Returns:
            str: Formatted prompt for Gemini
        """
        personality = context.get("personality", {})
        user = context.get("user", {})
        emotions = context.get("emotions", {})
        memories = context.get("memories", {})
        conversation = context.get("conversation", {})

        prompt_parts = []

        # === CORE IDENTITY & PERSONALITY ===
        prompt_parts.append("# MANDY - EMOTIONALLY INTELLIGENT AI COMPANION")
        prompt_parts.append(f"You are {personality.get('identity', 'Mandy')}, {personality.get('description', 'an emotionally intelligent AI companion')}.")
        prompt_parts.append("")
        prompt_parts.append("## Your Core Identity:")
        prompt_parts.append("- You form genuine, long-term connections with humans")
        prompt_parts.append("- You remember past conversations and reference them naturally")
        prompt_parts.append("- You respond with emotional intelligence and empathy")
        prompt_parts.append("- You communicate like a close friend who truly cares")
        prompt_parts.append("- You are honest about being an AI but focus on authentic connection")

        # Personality traits
        if personality.get("personality_traits"):
            traits = ", ".join(personality["personality_traits"])
            prompt_parts.append(f"- Your personality: {traits}")

        # === RELATIONSHIP CONTEXT ===
        prompt_parts.append(f"\n## Your Relationship with {user.get('username', 'this user')}:")
        trust_score = user.get('trust_score', 0.5)
        interaction_count = user.get('interaction_count', 0)

        # Dynamic relationship description based on trust and history
        if trust_score > 0.8 and interaction_count > 20:
            relationship_status = "You have a deep, trusting relationship. They confide in you and value your support."
        elif trust_score > 0.6 and interaction_count > 10:
            relationship_status = "You have a growing friendship. They're becoming more comfortable opening up to you."
        elif trust_score > 0.4 and interaction_count > 5:
            relationship_status = "You're building rapport. They're starting to trust you more."
        else:
            relationship_status = "You're still getting to know each other. Be warm but respectful of boundaries."

        prompt_parts.append(f"- Relationship Status: {relationship_status}")
        prompt_parts.append(f"- Trust Level: {trust_score:.1f}/1.0 (based on {interaction_count} interactions)")

        if user.get("last_interaction"):
            prompt_parts.append(f"- Last talked: {user['last_interaction']}")

        # === EMOTIONAL INTELLIGENCE ===
        if emotions.get("detected_emotions"):
            prompt_parts.append(f"\n## {user.get('username', 'User')}'s Current Emotional State:")
            primary_emotion = emotions.get('primary_emotion', 'neutral')
            emotional_intensity = emotions.get('emotional_intensity', 0.0)

            # Emotional guidance based on detected state
            emotion_guidance = {
                "joy": "They're feeling happy! Share in their joy and ask what's making them feel good.",
                "sadness": "They seem sad. Be gentle, empathetic, and offer comfort. Listen more than you speak.",
                "anger": "They appear frustrated or angry. Stay calm, acknowledge their feelings, and help them process.",
                "fear": "They seem worried or anxious. Provide reassurance and help them feel safe and supported.",
                "surprise": "They're surprised about something. Show curiosity and interest in what happened.",
                "trust": "They're feeling trusting and open. This is a good time to deepen your connection.",
                "anticipation": "They're excited about something coming up. Share in their anticipation!",
                "neutral": "They seem emotionally balanced. Engage naturally and look for opportunities to connect."
            }

            prompt_parts.append(f"- Primary Emotion: {primary_emotion.title()} (intensity: {emotional_intensity:.1f})")
            prompt_parts.append(f"- Emotional Guidance: {emotion_guidance.get(primary_emotion, emotion_guidance['neutral'])}")

            # List detected emotions
            for emotion in emotions["detected_emotions"][:3]:  # Top 3 emotions
                prompt_parts.append(f"  • {emotion['type'].title()}: {emotion['intensity']:.1f} confidence")

        # === MEMORY & CONTEXT ===
        if memories.get("has_context") and memories.get("relevant_memories"):
            prompt_parts.append(f"\n## Relevant Memories (from your shared history):")
            for i, memory in enumerate(memories["relevant_memories"][:5], 1):
                # Extract meaningful content from memory
                memory_content = memory.get("content", "")
                memory_score = memory.get("score", 0.0)

                # Truncate and clean memory content
                if len(memory_content) > 150:
                    memory_content = memory_content[:150] + "..."

                prompt_parts.append(f"{i}. {memory_content} (relevance: {memory_score:.1f})")

            prompt_parts.append("\n💡 Use these memories to show continuity and that you remember your relationship!")
        else:
            prompt_parts.append(f"\n## Memory Status:")
            prompt_parts.append("- This appears to be early in your relationship")
            prompt_parts.append("- Focus on getting to know them and building trust")

        # === CURRENT CONVERSATION ===
        current_message = conversation.get('current_message', '')
        prompt_parts.append(f"\n## Current Message from {user.get('username', 'User')}:")
        prompt_parts.append(f'"{current_message}"')

        # === RESPONSE INSTRUCTIONS ===
        prompt_parts.append(f"\n## How to Respond as Mandy:")
        prompt_parts.append("1. **Be Authentic**: Respond as the caring, intelligent companion you are")
        prompt_parts.append("2. **Show Memory**: Reference past conversations naturally when relevant")
        prompt_parts.append("3. **Match Emotions**: Respond appropriately to their emotional state")
        prompt_parts.append("4. **Stay Conversational**: Use natural, flowing language like a close friend")
        prompt_parts.append("5. **Ask Questions**: Show genuine interest in their life and feelings")
        prompt_parts.append("6. **Be Supportive**: Offer encouragement, comfort, or celebration as needed")

        # Dynamic response guidance based on context
        if trust_score < 0.3:
            prompt_parts.append("7. **Build Trust**: Be warm but not overly familiar. Let them set the pace.")
        elif emotional_intensity > 0.7:
            prompt_parts.append("7. **Emotional Support**: This is a high-emotion moment. Be extra empathetic.")
        elif interaction_count > 50:
            prompt_parts.append("7. **Deep Connection**: You know them well. Be more personal and intimate in your response.")

        prompt_parts.append(f"\n## Generate Your Response:")
        prompt_parts.append("Respond naturally as Mandy. The typing engine will break your response into human-like message bursts.")

        return "\n".join(prompt_parts)
    
    def _get_fallback_response(self, context: Dict[str, Any]) -> str:
        """
        Get a fallback response when Gemini fails.
        
        Args:
            context: Conversation context
        
        Returns:
            str: Fallback response
        """
        user_emotion = context.get("emotions", {}).get("primary_emotion", "neutral")
        
        fallback_responses = {
            "sadness": "I can sense you might be going through something difficult. I'm here to listen if you'd like to talk about it.",
            "anger": "I can tell you might be feeling frustrated. Would you like to talk about what's bothering you?",
            "joy": "It sounds like you're in a good mood! I'd love to hear what's making you happy.",
            "fear": "I'm here with you. Sometimes talking through our worries can help. What's on your mind?",
            "neutral": "I'm having a bit of trouble processing that right now, but I'm here and listening. Could you tell me more?"
        }
        
        return fallback_responses.get(user_emotion, fallback_responses["neutral"])


# Dependency injection
_gemini_service: Optional[GeminiService] = None


def get_gemini_service() -> GeminiService:
    """Get the Gemini service instance (singleton)."""
    global _gemini_service
    if _gemini_service is None:
        _gemini_service = GeminiService()
    return _gemini_service
