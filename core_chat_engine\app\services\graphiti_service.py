"""
Graphiti service for temporal knowledge graph operations.

This service handles all interactions with the Graphiti framework directly,
including episode management, memory search, and knowledge graph operations.
"""

import asyncio
import json
import os
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from uuid import UUID

from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType

from shared.models.base import Episode, SearchQuery, SearchResult
from shared.utils.config import get_config
from shared.utils.logging import get_logger, PerformanceTimer, log_database_operation


# Configuration and logger
config = get_config()
logger = get_logger("graphiti_service")


class GraphitiService:
    """Service for interacting with Graphiti temporal knowledge graph directly."""

    def __init__(self):
        """Initialize Graphiti service with direct client configuration."""
        # Initialize Graphiti client
        self.graphiti = None
        self._initialized = False

        logger.info("Graphiti service initialized")

    async def _ensure_initialized(self):
        """Ensure Graphiti client is initialized."""
        if not self._initialized:
            try:
                # Initialize Graphiti with Neo4j connection
                self.graphiti = Graphiti(
                    config.database.neo4j_uri,
                    config.database.neo4j_user,
                    config.database.neo4j_password
                )

                # Build indices and constraints (only needs to be done once)
                await self.graphiti.build_indices_and_constraints()

                self._initialized = True

                logger.info(
                    "Graphiti client initialized",
                    neo4j_uri=config.database.neo4j_uri
                )

            except Exception as e:
                logger.error(f"Failed to initialize Graphiti client: {str(e)}")
                raise
    
    async def add_episode(self, episode: Episode) -> UUID:
        """
        Add an episode to the knowledge graph.

        Args:
            episode: Episode to add to memory

        Returns:
            UUID: Episode ID
        """
        with PerformanceTimer("graphiti_add_episode", user_id=str(episode.user_id)):
            try:
                # Ensure Graphiti is initialized
                await self._ensure_initialized()

                # Convert episode content to string if it's a dict
                content = episode.content
                if isinstance(content, dict):
                    content = json.dumps(content)

                # Determine episode type
                episode_type = EpisodeType.json if isinstance(episode.content, dict) else EpisodeType.text

                # Add episode to Graphiti
                await self.graphiti.add_episode(
                    name=f"episode_{episode.id}",
                    episode_body=content,
                    source=episode_type,
                    source_description=episode.source,
                    reference_time=episode.created_at.replace(tzinfo=timezone.utc),
                    group_id=episode.group_id
                )

                log_database_operation(
                    operation="insert",
                    table="episodes",
                    duration_ms=0,  # Will be filled by PerformanceTimer
                    user_id=str(episode.user_id),
                    episode_id=str(episode.id)
                )

                logger.info(
                    "Added episode to Graphiti",
                    episode_id=str(episode.id),
                    user_id=str(episode.user_id),
                    group_id=episode.group_id,
                    content_length=len(str(content)),
                    episode_type=episode_type.value
                )

                return episode.id

            except Exception as e:
                logger.error(
                    f"Failed to add episode to Graphiti: {str(e)}",
                    episode_id=str(episode.id),
                    user_id=str(episode.user_id),
                    exc_info=True
                )
                raise
    
    async def search_relevant_context(
        self,
        query: str,
        user_id: UUID,
        group_id: str,
        limit: int = 10
    ) -> List[Dict]:
        """
        Search for relevant context from the knowledge graph.

        Args:
            query: Search query
            user_id: User ID for filtering
            group_id: Group ID for memory isolation
            limit: Maximum number of results

        Returns:
            List of relevant context items
        """
        with PerformanceTimer("graphiti_search", user_id=str(user_id)):
            try:
                # Ensure Graphiti is initialized
                await self._ensure_initialized()

                # Perform search using Graphiti
                search_results = await self.graphiti.search(
                    query=query,
                    group_id=group_id,
                    limit=limit
                )

                # Convert results to our format
                context_items = []
                for result in search_results:
                    context_items.append({
                        "content": result.fact,
                        "score": getattr(result, 'score', 1.0),
                        "timestamp": getattr(result, 'created_at', None),
                        "source": "graphiti",
                        "metadata": {
                            "uuid": str(result.uuid),
                            "source_node_uuid": str(result.source_node_uuid),
                            "target_node_uuid": str(result.target_node_uuid),
                            "valid_at": getattr(result, 'valid_at', None),
                            "invalid_at": getattr(result, 'invalid_at', None)
                        }
                    })

                log_database_operation(
                    operation="search",
                    table="knowledge_graph",
                    duration_ms=0,  # Will be filled by PerformanceTimer
                    user_id=str(user_id),
                    query=query,
                    results_count=len(context_items)
                )

                logger.info(
                    "Retrieved relevant context",
                    user_id=str(user_id),
                    group_id=group_id,
                    query=query,
                    results_count=len(context_items),
                    limit=limit
                )

                return context_items

            except Exception as e:
                logger.error(
                    f"Failed to search Graphiti: {str(e)}",
                    user_id=str(user_id),
                    query=query,
                    exc_info=True
                )
                return []
    
    async def search_memory(self, search_query: SearchQuery) -> List[SearchResult]:
        """
        Search memory using Graphiti's search capabilities.

        Args:
            search_query: Search query parameters

        Returns:
            List of search results
        """
        # Use the same search method as search_relevant_context
        context_items = await self.search_relevant_context(
            query=search_query.query,
            user_id=search_query.user_id,
            group_id=search_query.group_id,
            limit=search_query.limit
        )

        # Convert to SearchResult objects
        results = []
        for item in context_items:
            result = SearchResult(
                content=item["content"],
                score=item["score"],
                source=item["source"],
                metadata=item["metadata"]
            )
            results.append(result)

        return results
    
    async def update_user_metrics(
        self,
        user_id: UUID,
        trust_score: float,
        interaction_count: int
    ):
        """
        Update user metrics in the knowledge graph.

        Args:
            user_id: User ID
            trust_score: New trust score
            interaction_count: New interaction count
        """
        try:
            # For now, we'll log the metrics update
            # In the future, this could create a special episode about user metrics
            logger.info(
                "Updated user metrics",
                user_id=str(user_id),
                trust_score=trust_score,
                interaction_count=interaction_count
            )

        except Exception as e:
            logger.error(
                f"Failed to update user metrics: {str(e)}",
                user_id=str(user_id),
                exc_info=True
            )

    async def get_memory_statistics(
        self,
        user_id: UUID,
        group_id: str
    ) -> Dict[str, Any]:
        """
        Get memory statistics for a user.

        Args:
            user_id: User ID
            group_id: Group ID for memory isolation

        Returns:
            Dictionary with memory statistics
        """
        try:
            # For now, return basic statistics
            # In the future, this could query Graphiti for actual statistics
            return {
                "total_episodes": 0,
                "total_nodes": 0,
                "total_edges": 0,
                "memory_size_mb": 0.0,
                "last_updated": datetime.now(timezone.utc)
            }

        except Exception as e:
            logger.error(
                f"Failed to get memory statistics: {str(e)}",
                user_id=str(user_id),
                exc_info=True
            )
            return {}


# Dependency injection
_graphiti_service: Optional[GraphitiService] = None


def get_graphiti_service() -> GraphitiService:
    """Get the Graphiti service instance (singleton)."""
    global _graphiti_service
    if _graphiti_service is None:
        _graphiti_service = GraphitiService()
    return _graphiti_service


def get_graphiti_client() -> GraphitiService:
    """Get the Graphiti service for health checks."""
    return get_graphiti_service()
