"""
Typing style engine for human-like message delivery.

This service transforms AI-generated responses into more human-like
message bursts with natural typing delays and patterns.
"""

import random
import re
from typing import Dict, List, Optional, Any
from uuid import UUID

from shared.models.base import User, Emotion
from shared.utils.config import get_config
from shared.utils.logging import get_logger

from app.routes.chat import ChatMessage


# Configuration and logger
config = get_config()
logger = get_logger("typing_engine")


class TypingEngine:
    """
    Engine for creating human-like typing patterns and message bursts.
    
    This service makes Mandy's responses feel more natural by:
    1. Breaking long responses into multiple shorter messages
    2. Adding realistic typing delays based on message length
    3. Adjusting typing speed based on emotional context
    4. Adding natural pauses between message bursts
    """
    
    def __init__(self):
        """Initialize typing engine with configuration."""
        # Base typing speed (characters per second)
        self.base_typing_speed = 25.0
        
        # Speed variation range (percentage)
        self.speed_variation = 0.2
        
        # Minimum delay between messages (seconds)
        self.min_message_delay = 0.5
        
        # Maximum message length before splitting
        self.max_message_length = 150
        
        # Pause multiplier for different punctuation
        self.punctuation_pauses = {
            ".": 1.2,  # End of sentence
            "!": 1.3,  # Exclamation
            "?": 1.4,  # Question
            ",": 0.7,  # Comma
            ";": 0.9,  # Semicolon
            ":": 0.8,  # Colon
            "-": 0.6,  # Dash
            "...": 1.5,  # Ellipsis
        }
        
        # Emotion-based typing speed modifiers
        self.emotion_speed_modifiers = {
            "joy": 1.2,  # Faster when happy
            "sadness": 0.8,  # Slower when sad
            "anger": 1.3,  # Faster when angry
            "fear": 0.9,  # Slower when afraid
            "surprise": 1.1,  # Slightly faster when surprised
            "disgust": 0.9,  # Slower when disgusted
            "trust": 1.0,  # Normal speed when trusting
            "anticipation": 1.2,  # Faster when anticipating
            "neutral": 1.0,  # Normal speed when neutral
        }
        
        logger.info("Typing engine initialized")
    
    async def process_response(
        self,
        response: str,
        user: User,
        context: Dict[str, Any]
    ) -> List[ChatMessage]:
        """
        Process an AI response into human-like message bursts.
        
        Args:
            response: Raw response from Gemini
            user: User receiving the response
            context: Conversation context
        
        Returns:
            List of chat messages with typing delays
        """
        try:
            # Get emotional context for typing speed adjustment
            emotions = context.get("emotions", {})
            primary_emotion = emotions.get("primary_emotion", "neutral")
            
            # Split response into natural message chunks
            message_chunks = self._split_into_messages(response)
            
            # Calculate typing delays for each chunk
            typed_messages = []
            
            for i, chunk in enumerate(message_chunks):
                # Calculate typing delay based on length, emotion, and user preferences
                typing_delay = self._calculate_typing_delay(
                    message=chunk,
                    primary_emotion=primary_emotion,
                    user=user
                )
                
                # Create chat message
                chat_message = ChatMessage(
                    content=chunk,
                    typing_delay=typing_delay,
                    metadata={
                        "index": i,
                        "total": len(message_chunks)
                    }
                )
                
                typed_messages.append(chat_message)
            
            logger.info(
                "Processed response with typing engine",
                user_id=str(user.id),
                message_count=len(typed_messages),
                total_length=len(response),
                primary_emotion=primary_emotion
            )
            
            return typed_messages
            
        except Exception as e:
            logger.error(f"Typing engine error: {str(e)}")
            
            # Fallback: return single message with minimal delay
            return [
                ChatMessage(
                    content=response,
                    typing_delay=1.0,
                    metadata={"error": "typing_engine_failed"}
                )
            ]
    
    def _split_into_messages(self, response: str) -> List[str]:
        """
        Split a response into natural message chunks.
        
        This function breaks a long response into smaller, more natural
        message chunks based on sentence boundaries and length limits.
        
        Args:
            response: Raw response to split
        
        Returns:
            List of message chunks
        """
        # Clean up the response
        response = response.strip()
        
        # If response is already short, return as is
        if len(response) <= self.max_message_length:
            return [response]
        
        # Split by paragraph breaks first
        paragraphs = re.split(r'\n\s*\n', response)
        
        message_chunks = []
        
        for paragraph in paragraphs:
            # Skip empty paragraphs
            if not paragraph.strip():
                continue
            
            # If paragraph is short enough, add as is
            if len(paragraph) <= self.max_message_length:
                message_chunks.append(paragraph.strip())
                continue
            
            # Split paragraph into sentences
            sentences = re.split(r'(?<=[.!?])\s+', paragraph)
            
            current_chunk = ""
            
            for sentence in sentences:
                # If adding this sentence would exceed max length, start a new chunk
                if len(current_chunk) + len(sentence) > self.max_message_length:
                    if current_chunk:
                        message_chunks.append(current_chunk.strip())
                    
                    # If sentence itself is too long, split it further
                    if len(sentence) > self.max_message_length:
                        # Split by phrases (commas, semicolons, etc.)
                        phrases = re.split(r'(?<=[,;:])\s+', sentence)
                        
                        current_chunk = ""
                        
                        for phrase in phrases:
                            if len(current_chunk) + len(phrase) > self.max_message_length:
                                if current_chunk:
                                    message_chunks.append(current_chunk.strip())
                                
                                # If phrase itself is too long, just add it as is
                                if len(phrase) > self.max_message_length:
                                    message_chunks.append(phrase.strip())
                                    current_chunk = ""
                                else:
                                    current_chunk = phrase
                            else:
                                if current_chunk:
                                    current_chunk += " " + phrase
                                else:
                                    current_chunk = phrase
                        
                        if current_chunk:
                            message_chunks.append(current_chunk.strip())
                            current_chunk = ""
                    else:
                        current_chunk = sentence
                else:
                    if current_chunk:
                        current_chunk += " " + sentence
                    else:
                        current_chunk = sentence
            
            if current_chunk:
                message_chunks.append(current_chunk.strip())
        
        # Ensure we have at least one chunk
        if not message_chunks:
            message_chunks = [response]
        
        return message_chunks
    
    def _calculate_typing_delay(
        self,
        message: str,
        primary_emotion: str,
        user: User
    ) -> float:
        """
        Calculate realistic typing delay for a message.
        
        Args:
            message: Message content
            primary_emotion: Primary emotion for speed adjustment
            user: User for personalization
        
        Returns:
            Typing delay in seconds
        """
        # Base delay based on message length and typing speed
        base_delay = len(message) / self.base_typing_speed
        
        # Apply random variation
        variation = random.uniform(
            1.0 - self.speed_variation,
            1.0 + self.speed_variation
        )
        
        # Apply emotion-based speed modifier
        emotion_modifier = self.emotion_speed_modifiers.get(
            primary_emotion,
            1.0
        )
        
        # Apply user-specific modifiers (if any)
        user_modifier = user.preferences.get("typing_speed", 1.0)
        
        # Calculate final delay
        typing_delay = base_delay * variation * emotion_modifier * user_modifier
        
        # Ensure minimum delay
        typing_delay = max(typing_delay, self.min_message_delay)
        
        # Add extra delay for messages ending with certain punctuation
        for punct, multiplier in self.punctuation_pauses.items():
            if message.strip().endswith(punct):
                typing_delay *= multiplier
                break
        
        return typing_delay
    
    def _adjust_for_emotion(self, message: str, emotion: str) -> str:
        """
        Adjust message style based on emotion (optional enhancement).
        
        This could add subtle emotional markers like:
        - Exclamation points for joy
        - Ellipses for sadness
        - Capitalization for anger
        
        Args:
            message: Original message
            emotion: Emotion to adjust for
        
        Returns:
            Adjusted message
        """
        # This is a placeholder for potential future enhancement
        return message


# Dependency injection
_typing_engine: Optional[TypingEngine] = None


def get_typing_engine() -> TypingEngine:
    """Get the typing engine instance (singleton)."""
    global _typing_engine
    if _typing_engine is None:
        _typing_engine = TypingEngine()
    return _typing_engine
