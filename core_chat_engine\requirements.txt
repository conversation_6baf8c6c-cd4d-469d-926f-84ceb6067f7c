# Core Chat Engine Requirements
# Mandy AI Companion - Core conversational service

# --- Core Web Framework ---
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0
pydantic==2.5.0
pydantic-settings==2.1.0

# --- AI & Graph Intelligence ---
google-generativeai==0.3.2
# Install graphiti-core directly from the GitHub repository for latest features
git+https://github.com/getzep/graphiti.git#egg=graphiti-core&subdirectory=graphiti_core

# --- Communication & Utilities ---
httpx==0.25.2  # For async API calls to the Graphiti Server
loguru==0.7.2  # For structured, readable logging
python-jose[cryptography]==3.3.0  # For JWT handling
passlib[bcrypt]==1.7.4  # For password hashing
python-multipart==0.0.6
python-dotenv==1.0.0

# --- Neo4j Driver (for direct inspection/debugging) ---
neo4j==5.15.0

# --- Additional Utilities ---
python-dateutil==2.8.2
orjson==3.9.10
psutil==5.9.6
cryptography==41.0.8
