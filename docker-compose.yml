version: '3.8'

services:
  # Neo4j Database - <PERSON>'s Physical Brain
  neo4j:
    image: neo4j:5.26
    container_name: mandy-neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/password
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,algo.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - mandy-network
    healthcheck:
      test: ["CMD", "wget", "-O", "/dev/null", "-q", "http://localhost:7474"]
      interval: 10s
      timeout: 5s
      retries: 5



  # Core Chat Engine - Main Conversation Orchestrator
  core_chat_engine:
    build:
      context: ./core_chat_engine
      dockerfile: Dockerfile
    container_name: mandy-core
    ports:
      - "8000:8000"
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
    volumes:
      - ./core_chat_engine:/app
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - mandy-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Content Fetcher Worker - Retrieves and processes external content
  content_fetcher:
    build:
      context: ./workers
      dockerfile: Dockerfile
    container_name: mandy-content-fetcher
    command: python -m content_fetcher.main
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password
      - CORE_API_URL=http://core_chat_engine:8000
      - SCHEDULER_INTERVAL=3600
      - LOG_LEVEL=INFO
    volumes:
      - ./workers:/app
    depends_on:
      core_chat_engine:
        condition: service_healthy
    networks:
      - mandy-network

  # Reflection Engine Worker - Analyzes conversations and updates trust model
  reflection_engine:
    build:
      context: ./workers
      dockerfile: Dockerfile
    container_name: mandy-reflection
    command: python -m reflection_engine.main
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password
      - CORE_API_URL=http://core_chat_engine:8000
      - SCHEDULER_INTERVAL=1800
      - LOG_LEVEL=INFO
    volumes:
      - ./workers:/app
    depends_on:
      core_chat_engine:
        condition: service_healthy
    networks:
      - mandy-network

  # Gradio Web UI - User Interface
  ui:
    build:
      context: ./ui
      dockerfile: Dockerfile
    container_name: mandy-ui
    ports:
      - "7860:7860"
    environment:
      - CORE_API_URL=http://core_chat_engine:8000
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=development
      - LOG_LEVEL=INFO
    volumes:
      - ./ui:/app
    depends_on:
      core_chat_engine:
        condition: service_healthy
    networks:
      - mandy-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:

networks:
  mandy-network:
    driver: bridge
